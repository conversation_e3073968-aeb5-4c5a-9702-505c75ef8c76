import { login, logout } from "../actions/login";
import exec from "k6/execution";

import {
  listCreditCards,
  getCreditCardTransactions,
  getCreditCardDetails,
} from "../actions/creditCards";
import { fail, sleep } from "k6";
import { callHomepage } from "../actions/homepage";
import {
  //addUserToExcluded,
  getNextUser,
} from "../core/helpers/getExcludedArray";
import { getTargetVus } from "../core/helpers/getTargetVus";

const waitTimeAfterLogin = 30;
const waitTimeAfterVu = 113;

export function creditCardTransactionExec() {
  const user: any = getNextUser(
    exec.vu.idInTest,
    globalThis.creditCardSubUsers,
  );
  let customHeaders: any = null;
  try {
    if (!user.username || !user.password) {
      throw new Error(
        `User with id ${exec.vu.idInTest} has invalid username or password. Data file: creditCardSubUsers`,
      );
    }
    const randomString = Math.random().toString(36).slice(2, 7)

    const deviceId = `${user.username}_${randomString}`;
    const token = login(user.username, user.password, deviceId);
    sleep(waitTimeAfterLogin);
    if (token) {
      customHeaders = {token, deviceId}
      callHomepage(customHeaders);
      var cardList: any = listCreditCards(customHeaders);
      if (!cardList || cardList?.length === 0) {
        throw new Error(
          "Use Case Failure - The user doesnt hold a valid credit card",
        );
      }
      var accountNumber = cardList[0].accountNumber;
      if (!accountNumber) {
        throw new Error(
          "Use Case Failure - The user does not have a credit card",
        );
      }
      getCreditCardDetails(accountNumber, customHeaders);
      getCreditCardTransactions(accountNumber, customHeaders);
    } else {
      throw new Error("Authentication Failure - The user was not logged in");
    }
  } catch (error: any) {

    //addUserToExcluded(user);
    sleep(waitTimeAfterVu * 3);
    fail(`Unexpected error: ${error?.message}`);
  } finally {
    if (customHeaders) {
      logout(customHeaders);
    }
    sleep(waitTimeAfterVu);
  }
}

export function creditCardTransaction(
  type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
  const targetVUs = getTargetVus(type, "creditCardTransaction");
  switch (type) {
    case "load":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "15m", target: targetVUs },
          { duration: "50m", target: targetVUs },
        ],
        exec: creditCardTransactionExec.name,
      };
    case "endurance":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        stages: [
          { duration: "1h", target: targetVUs },
          { duration: "12h", target: targetVUs },
          { duration: "1h", target: 0 },
        ],
        exec: creditCardTransactionExec.name,
      };
    case "smoke":
      return {
        executor: "per-vu-iterations",
        vus: 1,
        iterations: 1,
        exec: creditCardTransactionExec.name,
      };
    case "shock":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "5m", target: targetVUs },
          { duration: "5m", target: targetVUs },
        ],
        exec: creditCardTransactionExec.name,
      };
    default:
      throw new Error("Invalid type");
  }
}
