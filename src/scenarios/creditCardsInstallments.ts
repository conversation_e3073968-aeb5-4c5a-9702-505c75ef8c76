import { login, logout } from "../actions/login";
import exec from "k6/execution";

import {
  listCreditCards,
  getCreditCardTransactions,
  getCreditCardDetails,
  getCreditCardInstallment,
} from "../actions/creditCards";
import { fail, sleep } from "k6";
import { callHomepage } from "../actions/homepage";
import {
  //addUserToExcluded,
  getNextUser,
} from "../core/helpers/getExcludedArray";
import { getTargetVus } from "../core/helpers/getTargetVus";

const waitTimeAfterLogin = 30;
const waitTimeAfterVu = 139;

export function creditCardInstallementExec() {
  const user: any = getNextUser(
    exec.vu.idInTest,
    globalThis.creditCardsInstallmentSubUser,
  );
  let customHeaders: any = null;
  try {
    if (!user.username || !user.password) {
      throw new Error(
        `User with id ${exec.vu.idInTest} has invalid username or password. Data file: creditCardsInstallmentSubUser`,
      );
    }
    const randomString = Math.random().toString(36).slice(2, 7)

    const deviceId = `${user.username}_${randomString}`;
    const token = login(user.username, user.password, deviceId);
    sleep(waitTimeAfterLogin);
    if (token) {
      customHeaders = {token, deviceId}
      callHomepage(customHeaders);
      var cardList: any = listCreditCards(customHeaders);
      if (cardList.length === 0 || !cardList[0].accountNumber) {
        throw new Error(
          "Use Case Failure - The user doesnt hold an eligible credit card",
        );
      }
      var accountNumber = cardList[0].accountNumber;
      getCreditCardDetails(accountNumber, customHeaders);
      getCreditCardTransactions(accountNumber, customHeaders);
      getCreditCardInstallment(accountNumber, customHeaders);
    } else {
      throw new Error("Authentication Failure - The user was not logged in");
    }
  } catch (error: any) {

    //addUserToExcluded(user);
    sleep(waitTimeAfterVu * 3);
    fail(`Unexpected error: ${error?.message}`);
  } finally {
    if (customHeaders) {
      logout(customHeaders);
    }
    sleep(waitTimeAfterVu);
  }
}

export function creditCardInstallment(
  type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
  const targetVUs = getTargetVus(type, "creditCardInstallment");
  switch (type) {
    case "load":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "15m", target: targetVUs },
          { duration: "50m", target: targetVUs },
        ],
        exec: creditCardInstallementExec.name,
      };
    case "endurance":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        stages: [
          { duration: "1h", target: targetVUs },
          { duration: "12h", target: targetVUs },
          { duration: "1h", target: 0 },
        ],
        exec: creditCardInstallementExec.name,
      };
    case "smoke":
      return {
        executor: "per-vu-iterations",
        vus: 1,
        iterations: 1,
        exec: creditCardInstallementExec.name,
      };
    case "shock":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "60s",
        stages: [
          { duration: "5m", target: targetVUs },
          { duration: "5m", target: targetVUs },
        ],
        exec: creditCardInstallementExec.name,
      };
    default:
      throw new Error("Invalid type");
  }
}
