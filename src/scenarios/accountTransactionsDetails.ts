import { login, logout } from "../actions/login";
import exec from "k6/execution";
import {
  getAccountTransactionsDetails,
  listAccounts,
  getAccountTransactions,
  getAccountDetails,
  getHistoryAccountTransactions,
} from "../actions/accounts";
import { sleep } from "k6";
import { fail } from "k6";
import { callHomepage } from "../actions/homepage";
import {
  //addUserToExcluded,
  getNextUser,
} from "../core/helpers/getExcludedArray";
import { getTargetVus } from "../core/helpers/getTargetVus";

const waitTimeAfterLogin = 30;
const waitTimeAfterVu = 152;

export function accountTransactionDetailsExec() {
  const user: any = getNextUser(
    exec.vu.idInTest,
    globalThis.accountsTransactionsSubUsers,
  );
  let customHeaders: any = null;
  try {
    if (!user.username || !user.password) {
      throw new Error(
        `User with id ${exec.vu.idInTest} has invalid username or password. Data file: accountsTransactionsSubUsers`,
      );
    }
    const randomString = Math.random().toString(36).slice(2, 7)

    const deviceId = `${user.username}_${randomString}`;    const token = login(user.username, user.password, deviceId);
    sleep(waitTimeAfterLogin);
    if (token) {
      customHeaders = {token, deviceId}
      callHomepage(customHeaders);
      const accountsListData: any = listAccounts(customHeaders);
      if (accountsListData.accounts.length === 0) {
        throw new Error(
          "Use Case Failure - The user doesnt hold an eligible account",
        );
      }
      const accountNumber = accountsListData.accounts[0].accountNumber;
      const accountId = accountsListData.accounts[0].id;
      getAccountDetails(accountNumber, customHeaders);
      let transactionDetailslistData: any = getAccountTransactions(
        accountId,
        customHeaders,
      );

      if (!transactionDetailslistData || transactionDetailslistData?.length === 0) {
        transactionDetailslistData = getHistoryAccountTransactions(accountId, customHeaders);
      }

      if (!transactionDetailslistData || transactionDetailslistData?.length === 0) {
        throw new Error(
          "Use Case Failure - The user doesnt hold an eligible transaction",
        );
      }
      const transactinID = transactionDetailslistData[0].id;
      getAccountTransactionsDetails(transactinID, customHeaders);
      sleep(10)
      listAccounts(customHeaders)
    } else {
      throw new Error(`Authentication Failure - The user was not logged in`);
    }
  } catch (error: any) {
    if (
      error.message.includes("Authentication Failure") ||
      error.message.includes("Use Case Failure")
    ) {
      console.log(
        `Bad test data: ${exec.scenario.name}\t ${user.username} - ${error?.message} - ${error?.status}`,
      );
    }
    //addUserToExcluded(user);
    sleep(waitTimeAfterVu * 3);
    fail(`Unexpected error: ${error?.message}`);
  } finally {
    if (customHeaders) {
      logout(customHeaders);
    }
    sleep(waitTimeAfterVu);
  }
}

export function accountTransactionDetails(
  type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
  const targetVUs = getTargetVus(type, "accountTransactionDetails");
  switch (type) {
    case "load":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "15m", target: targetVUs },
          { duration: "50m", target: targetVUs },
        ],
        exec: accountTransactionDetailsExec.name,
      };
    case "endurance":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "300s",
        stages: [
          { duration: "1h", target: targetVUs },
          { duration: "12h", target: targetVUs },
          { duration: "1h", target: 0 },
        ],
        exec: accountTransactionDetailsExec.name,
      };
    case "smoke":
      return {
        executor: "per-vu-iterations",
        vus: 1,
        iterations: 1,
        exec: accountTransactionDetailsExec.name,
      };
    case "shock":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "5m", target: targetVUs },
          { duration: "5m", target: targetVUs },
        ],
        exec: accountTransactionDetailsExec.name,
      };
    default:
      throw new Error("Invalid type");
  }
}
