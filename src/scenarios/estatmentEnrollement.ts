import { getUserDetails } from "../actions/users";
import { login, logout } from "../actions/login";
import exec from "k6/execution";
import {
  statmentEntrollConfirmation,
  statmentEntrollmentPreCondition,
} from "../actions/statments";
import { fail, sleep } from "k6";
import { callHomepage } from "../actions/homepage";
import {
  //addUserToExcluded,
  getNextUser,
} from "../core/helpers/getExcludedArray";
import { getTargetVus } from "../core/helpers/getTargetVus";

const waitTimeAfterLogin = 30;
const waitTimeAfterVu = 126;

export function estatementEnrollmentExec() {
  const user: any = getNextUser(
    exec.vu.idInTest,
    globalThis.offlineRequestSubUsers,
  );
  let customHeaders: any = null;

  try {
    if (!user.username || !user.password) {
      throw new Error(
        `User with id ${exec.vu.idInTest} has invalid username or password. Data file: offlineRequestSubUsers`,
      );
    }
    const randomString = Math.random().toString(36).slice(2, 7)

    const deviceId = `${user.username}_${randomString}`;
    const token = login(user.username, user.password, deviceId);
    sleep(waitTimeAfterLogin);
    if (token) {
      customHeaders = {token, deviceId}
      callHomepage(customHeaders);
      const email = getUserDetails(customHeaders);
      if (!email) {
        throw new Error("Use Case Failure - The user doesnt have an email");
      }
      const signature = statmentEntrollmentPreCondition(email, customHeaders);
      statmentEntrollConfirmation(email, signature, customHeaders);
    } else {
      throw new Error("Authentication Failure - The user was not logged in");
    }
  } catch (error: any) {

    //addUserToExcluded(user);
    sleep(waitTimeAfterVu * 3);
    fail(`Unexpected error: ${error?.message}`);
  } finally {
    if (customHeaders) {
      logout(customHeaders);
    }
    sleep(waitTimeAfterVu);
  }
}

export function estatementEnrollment(
  type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
  const targetVUs = getTargetVus(type, "estatementEnrollment");
  switch (type) {
    case "load":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "15m", target: targetVUs },
          { duration: "50m", target: targetVUs },
        ],
        exec: estatementEnrollmentExec.name,
      };
    case "endurance":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        stages: [
          { duration: "1h", target: targetVUs },
          { duration: "12h", target: targetVUs },
          { duration: "1h", target: 0 },
        ],
        exec: estatementEnrollmentExec.name,
      };
    case "smoke":
      return {
        executor: "per-vu-iterations",
        vus: 1,
        iterations: 1,
        exec: estatementEnrollmentExec.name,
      };
    case "shock":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "5m", target: targetVUs },
          { duration: "5m", target: targetVUs },
        ],
        exec: estatementEnrollmentExec.name,
      };
    default:
      throw new Error("Invalid type");
  }
}
