import { SharedArray } from "k6/data";
import { RunLoad, ScenarioLoad } from "../typings/globals";

export const virtualUsers = new SharedArray("virtual-users", function () {
  return [
    {
      type: "load",
      load: 4000,
    },
    {
      type: "endurance",
      load: 5000,
    },
    {
      type: "shock",
      load: 3000,
    },
  ] as RunLoad[];
});

export const maxVirtualUsers = virtualUsers.reduce(
  // return the largest load
  (max, vu) => (vu.load > max ? vu.load : max),
  0,
);

// Change this to reflect percentage of each scenario of total load in real life (bonus points to create feedback loop)
export const scenarioLoadPercentages = new SharedArray(
  "load-percentages",
  function () {
    return [
      {
     scenario: "accessHomePage",
      percentage: 0.*********,
      },
     {
       scenario: "accountTransaction",
       percentage: 0.*********        ,
     },
     {
       scenario: "accountTransactionDetails",
       percentage: 0.*********,
     },
     {
       scenario: "bookCertificateDeposit",
       percentage: 0.*********,
     },
     {
       scenario: "billPayment",
       percentage: 0.*********,
     },
     {
       scenario: "creditCardTransfer",
       percentage: 0.*********,
     },
     {
       scenario: "createTransferInsideCIB",
       percentage: 0.********,
     },
     {
       scenario: "createOutSideCibTransfer",
       percentage: 0.********,
     },
     {
       scenario: "creditCardInstallment",
       percentage: 0.*********,
     },
     {
       scenario: "creditCardTransaction",
       percentage: 0.*********,
     },
     {
       scenario: "depositDetails",
       percentage: 0.*********,
     },
     {
       scenario: "estatementEnrollment",
       percentage: 0.*********,
     },
     {
       scenario: "debitCardList",
       percentage: 0.*********,
     },
     {
       scenario: "ipnTransfer",
       percentage: 0.568863434,
     },
     {
       scenario: "annexDownload",
       percentage: 0.000483727,
     },
        {
       scenario: "cardManagement",
       percentage: 0.000483727,
     },{
            scenario: "debitCardIssuance",
            percentage: 0.000483727,
        },
     //{
       //scenario:  "BiometricLogin",
       //percentage: 0.179954,
     //}
   ];
  },
);

export const loadArray = new SharedArray("scenario-loads", function () {
  //map virtual users and scenarioLoadPercentages together to give {type, scenario, load=type.load*percentage}
  const loads: ScenarioLoad[] = [];
  virtualUsers.forEach((vu: RunLoad) => {
    scenarioLoadPercentages.forEach((scenario) => {
      loads.push({
        type: vu.type,
        scenario: scenario.scenario,
        load: Math.floor(
          vu.load *
            (scenarioLoadPercentages?.find(
              (s) => s.scenario == scenario.scenario,
            )?.percentage ?? 1),
        ),
      });
    });
  });
  return loads as ScenarioLoad[];
});
